package me.zivush.smp.economy;

import me.zivush.smp.SMP;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Manages Multiworld Money plugin integration for SMP economy separation
 */
public class MultiworldMoneyManager {
    
    private final SMP plugin;
    private final Set<String> pendingEconomyResets = new HashSet<>();
    
    public MultiworldMoneyManager(SMP plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Creates a new economy group for an SMP
     * @param smpName The name of the SMP
     */
    public void createEconomyGroup(String smpName) {
        String command = "themultiworldmoney group add " + smpName;
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        plugin.getLogger().info("Created economy group for SMP: " + smpName);
    }
    
    /**
     * Deletes an economy group for an SMP
     * @param smpName The name of the SMP
     */
    public void deleteEconomyGroup(String smpName) {
        String command = "themultiworldmoney group delete " + smpName;
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        plugin.getLogger().info("Deleted economy group for SMP: " + smpName);
    }
    
    /**
     * Adds a world to an SMP's economy group
     * @param worldName The name of the world to add
     * @param smpName The name of the SMP
     */
    public void addWorldToGroup(String worldName, String smpName) {
        String command = "themultiworldmoney group move " + worldName + " " + smpName;
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        plugin.getLogger().info("Added world " + worldName + " to economy group: " + smpName);
    }
    
    /**
     * Adds all SMP worlds to the economy group
     * @param smpName The name of the SMP
     */
    public void addAllSMPWorldsToGroup(String smpName) {
        String[] worldTypes = {"", "_nether", "_the_end"};
        
        for (String type : worldTypes) {
            String worldName = plugin.generateWorldName(smpName, type);
            addWorldToGroup(worldName, smpName);
        }
    }
    
    /**
     * Resets a player's economy balance
     * @param player The player whose balance to reset
     */
    public void resetPlayerEconomy(Player player) {
        String command = "eco reset " + player.getName();
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        plugin.getLogger().info("Reset economy for player: " + player.getName());
    }
    
    /**
     * Checks if a player is joining an SMP for the first time and resets their economy if needed
     * @param player The player joining
     * @param smpName The name of the SMP
     */
    public void handlePlayerFirstJoin(Player player, String smpName) {
        UUID playerUUID = player.getUniqueId();
        String playerKey = "smps." + smpName + ".economy_reset_players";
        
        // Get list of players who have had their economy reset for this SMP
        Set<String> resetPlayers = new HashSet<>(plugin.getDatabase().getStringList(playerKey));
        
        // Check if this player has already had their economy reset for this SMP
        if (!resetPlayers.contains(playerUUID.toString())) {
            // Add a small delay to ensure the player is fully loaded into the world
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Double-check the player is still online and in the SMP world
                    if (player.isOnline() && plugin.isWorldInSMP(player.getWorld().getName())) {
                        resetPlayerEconomy(player);
                        
                        // Mark this player as having their economy reset for this SMP
                        resetPlayers.add(playerUUID.toString());
                        plugin.getDatabase().set(playerKey, resetPlayers.toArray(new String[0]));
                        
                        try {
                            plugin.getDatabase().save(plugin.getDatabaseFile());
                        } catch (Exception e) {
                            plugin.getLogger().warning("Failed to save database after economy reset: " + e.getMessage());
                        }
                        
                        plugin.getLogger().info("Reset economy for first-time SMP join: " + player.getName() + " in " + smpName);
                    }
                }
            }.runTaskLater(plugin, 20L); // 1 second delay
        }
    }
    
    /**
     * Handles SMP renaming by creating new group and moving worlds
     * @param oldSmpName The old SMP name
     * @param newSmpName The new SMP name
     */
    public void handleSMPRename(String oldSmpName, String newSmpName) {
        // Create new economy group
        createEconomyGroup(newSmpName);
        
        // Add a delay to ensure the group is created before moving worlds
        new BukkitRunnable() {
            @Override
            public void run() {
                // Move all worlds to new group
                addAllSMPWorldsToGroup(newSmpName);
                
                // Add another delay before deleting old group
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        // Delete old economy group
                        deleteEconomyGroup(oldSmpName);
                    }
                }.runTaskLater(plugin, 40L); // 2 second delay
            }
        }.runTaskLater(plugin, 20L); // 1 second delay
    }
    
    /**
     * Cleans up economy data when an SMP is deleted
     * @param smpName The name of the SMP being deleted
     */
    public void cleanupSMPEconomy(String smpName) {
        // Delete the economy group
        deleteEconomyGroup(smpName);
        
        // Remove economy reset tracking data
        String playerKey = "smps." + smpName + ".economy_reset_players";
        plugin.getDatabase().set(playerKey, null);
        
        try {
            plugin.getDatabase().save(plugin.getDatabaseFile());
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to save database after economy cleanup: " + e.getMessage());
        }
    }
}
