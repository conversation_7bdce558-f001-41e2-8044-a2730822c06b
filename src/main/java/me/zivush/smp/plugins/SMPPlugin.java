package me.zivush.smp.plugins;

import java.util.List;

public class SMPPlugin {
    private final String id;
    private final List<String> permissions;
    private final List<String> commands;
    private final PluginIcon icon;

    public SMPPlugin(String id, List<String> permissions, List<String> commands, PluginIcon icon) {
        this.id = id;
        this.permissions = permissions;
        this.commands = commands;
        this.icon = icon;
    }

    public String getId() {
        return id;
    }

    public List<String> getPermissions() {
        return permissions;
    }

    public List<String> getCommands() {
        return commands;
    }

    public PluginIcon getIcon() {
        return icon;
    }

    public static class PluginIcon {
        private final String material;
        private final String name;
        private final List<String> lore;
        private final int customModelData;

        public PluginIcon(String material, String name, List<String> lore, int customModelData) {
            this.material = material;
            this.name = name;
            this.lore = lore;
            this.customModelData = customModelData;
        }

        public String getMaterial() {
            return material;
        }

        public String getName() {
            return name;
        }

        public List<String> getLore() {
            return lore;
        }

        public int getCustomModelData() {
            return customModelData;
        }
    }
}
