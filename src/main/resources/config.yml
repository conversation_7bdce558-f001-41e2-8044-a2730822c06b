messages:
  prefix: "&8[&bSMP&8] "
  smp_created: "&aSuccessfully created SMP &e%name%"
  smp_joined: "&aSuccessfully joined SMP &e%name%"
  already_in_smp: "&cYou are already in SMP &e%name%&c!"
  smp_exists: "&cAn SMP with this name already exists!"
  no_permission: "&cYou don't have permission!"
  max_smps_reached: "&cYou've reached your maximum SMP limit!"
  smp_not_found: "&cThis SMP doesn't exist!"
  invalid_difficulty: "&cInvalid difficulty! Use: peaceful/easy/normal/hard"
  invalid_privacy: "&cInvalid privacy setting! Use: private/public"
  max_players_reached: "&cThis SMP has reached its maximum player capacity!"
  not_in_clan: "&cYou must be in the same clan as the owner to join this private SMP!"
  command_disabled: "&cThis command is disabled while in an SMP world!"

  # Kick and Ban messages
  not_in_smp_world: "&cYou must be in an SMP world to use this command!"
  not_smp_owner: "&cOnly the SMP owner can use this command!"
  player_not_found: "&cPlayer not found!"
  player_not_in_smp: "&cThat player is not in this SMP!"
  cannot_kick_owner: "&cYou cannot kick the SMP owner!"
  cannot_ban_owner: "&cYou cannot ban the SMP owner!"
  kicked_from_smp: "&cYou have been kicked from SMP &e%smp% &cby &e%owner%&c!"
  player_kicked: "&aSuccessfully kicked &e%player% &afrom SMP &e%smp%&a!"
  banned_from_smp: "&cYou have been banned from SMP &e%smp% &cby &e%owner%&c!"
  banned_from_smp_join: "&cYou are banned from SMP &e%smp%&c!"
  player_banned: "&aSuccessfully banned &e%player% &afrom SMP &e%smp%&a!"
  player_already_banned: "&cThat player is already banned from this SMP!"
  player_not_banned: "&cThat player is not banned from this SMP!"
  player_unbanned: "&aSuccessfully unbanned &e%player% &afrom SMP &e%smp%&a!"
  ban_save_error: "&cError saving ban to database!"
  unban_save_error: "&cError saving unban to database!"
  smp_deleted: "&cSMP &e%name% &chas been deleted"
  smp_privacy_updated: "&aPrivacy setting updated to: &e%privacy%"
  smp_difficulty_updated: "&aDifficulty updated to: &e%difficulty%"
  no_settings_permission: "&cYou don't have permission to modify this SMP's settings!"
  delete_cooldown: "&cYou must wait for world creation to complete before deleting this SMP!"
  creating_smp: "&aCreating your SMP &e%name%&a... This may take a moment!"
  smp_creation_pending: "&eYour SMP &a%name% &eis pending approval. Please wait..."
  agreement_declined: "&cSMP creation cancelled. You must accept the agreement to create an SMP."
  enter_smp_name: "&ePlease enter a name for your SMP in chat:"
  name_too_long: "&cSMP name cannot be longer than 16 characters!"
  invalid_name: "&cInvalid SMP name! Use only letters, numbers and underscores."
  display_name_updated: "&aSMP display name updated to: &e%name%"
  no_rename_permission: "&cYou don't have permission to rename SMPs!"
  name_exists: "&cAn SMP with this name already exists!"
  anvil_title: "&8Enter SMP Name"
  anvil_invalid_name: "&cInvalid name! Try again"
  anvil_name_exists: "&cThis name already exists! Try again"
  not_in_clan_create: "&cYou must be in a clan to create a private SMP!"
  plugin_not_found: "&cPlugin configuration not found!"
  plugin_enabled: "&aPlugin %plugin% has been enabled!"
  plugin_disabled: "&cPlugin %plugin% has been disabled!"
  max_plugins_reached: "&cYou've reached your maximum plugin limit!"
  plugin_commands_enabled: "&aPlugin commands for %plugin% are now available!"
  smp_world_deleted_teleporting: "&eThe SMP world '%name%' you were in is being deleted. Teleporting you to spawn."
  creation_cooldown_active: "&cYou must wait %time% before creating another SMP!"
  spawn_cooldown_active: "&cYou're in combat! Cannot use /spawn for %time%."
  cross_smp_command_blocked: "&cYou cannot target players outside your current SMP with this command."
  smp_creation_complete_teleporting: "&aSMP '%name%' created! Teleporting you now..."
  safety_mode_enabled: "&a[SMP] &eSafety mode enabled during world creation. Your items and XP will be protected if you die."
  safety_mode_disabled: "&a[SMP] &eSafety mode disabled. World creation completed."
  join_cooldown_active: "&cYou must wait %time% before joining another SMP!"
  combat_join_cooldown_active: "&cYou're in combat! Cannot join an SMP for %time%."
  command_cooldown_active: "&cYou must wait %time% before using %command% again!"
  smp_inactive: "&eSMP &a%name% &eis now inactive and fully editable."
  smp_auto_deleted: "&cSMP &e%name% &chas been automatically deleted due to inactivity."
  smp_inactivity_notification: "&eSMP &a%name% &ehas been inactive for &c%days% &edays."
  smp_deleted_notification: "&cSMP &e%name% &chas been deleted."

# Inactivity settings
inactivity:
  enabled: true # Set to false to disable inactivity tracking
  duration: 15 # Minutes with less than 1 player to be considered inactive
  check_interval: 3600 # Seconds between inactivity checks (3600 = hourly)
  auto_delete: 30 # Days of inactivity before auto-deletion
  # Thresholds for inactivity notifications (in days)
  # These are the days at which the velocity command will be executed
  thresholds: [4, 8, 11, 14, 17, 20, 23, 26, 28]

plugins:
  example_plugin:
    permissions:
      - example_perm.1
      - example_perm.2
    commands:
      - examplecmd
      - examplecmd2
    icon:
      material: DIAMOND_SWORD
      name: "&bExample Plugin"
      lore:
        - "&7This is an example plugin"
        - "&7It does cool things!"
      custom_model_data: 1

  worldedit:
    permissions:
      - worldedit.wand
      - worldedit.selection
    commands:
      - /
      - //
      - pos1
      - pos2
      - wand
      - worldedit
      - we
    icon:
      material: WOODEN_AXE
      name: "&6WorldEdit"
      lore:
        - "&7World editing tools"
        - "&7For building and terraforming"
      custom_model_data: 0

  essentials:
    permissions:
      - essentials.home
      - essentials.tpa
    commands:
      - home
      - sethome
      - tpa
      - tpahere
      - tpaccept
      - tpdeny
      - back
      - warp
    icon:
      material: COMPASS
      name: "&aEssentials"
      lore:
        - "&7Basic server utilities"
        - "&7For quality of life"
      custom_model_data: 2

world_creation:
  title: "&b&lGenerating your SMP..."
  subtitle: "&7This may take a while"
  nether_delay: 10
  end_delay: 20
  join_delay: 5
  keep_inventory_duration: 10
  death_book:
    title: "&c&lDeath During World Creation"
    author: "Server"
    pages:
      - "&cYou died during world creation!\n\n&7Due to potential lag, your items and XP have been saved and you've been teleported to safety.\n\n&7You have 30 seconds of immunity."
  immunity:
    bossbar:
      title: "&aSafe Mode: &b%seconds%s"
      color: GREEN
      style: SOLID


world_border:
  check_interval: 10

combat_spawn_cooldown: 15 # Cooldown in seconds before using /spawn after taking damage in an SMP
creation_cooldown: 300 # Cooldown in seconds between creating SMPs for a player

# Safe respawn settings
safe_respawn:
  enabled: true # Set to false to disable teleporting to a safe location after death
  search_radius: 20 # Maximum radius to search for a safe location around death point

gui:
  smp_plugins:
    title: "&8Plugin Manager"
    size: 54
    plugins:
      slots: [10,11,12,13,14,15,16,19,20,21,22,23,24,25,28,29,30,31,32,33,34,37,38,39,40,41,42,43]
      enabled_lore:
        - "&aPlugin is enabled"
        - "&7Right Click to change access"
        - "&7Left Click to disable"
      disabled_lore:
        - "&cPlugin is disabled"
        - "&7Click to enable"
    next_page:
      material: ARROW
      name: "&aNext Page"
      slot: 50
      lore:
        - "&7Click to go to next page"
      custom_model_data: 0
    previous_page:
      material: ARROW
      name: "&aPrevious Page"
      slot: 48
      lore:
        - "&7Click to go to previous page"
      custom_model_data: 0
    search:
      material: ANVIL
      name: "&eSearch Plugins"
      slot: 49
      lore:
        - "&7Click to search plugins"
      title: "&8Search Plugins"
      text: "Search plugins..."
      custom_model_data: 0
    close:
      material: BARRIER
      name: "&cClose"
      slot: 53
      lore:
        - "&7Click to close"
      custom_model_data: 0
    filler:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      custom_model_data: 0
  plugin_access:
    title: "&8Plugin Access Settings"
    size: 27
    everyone:
      material: PLAYER_HEAD
      name: "&aFor Everyone"
      slot: 11
      lore:
        - "&7Allow all players to"
        - "&7use this plugin"
    owner_only:
      material: SKELETON_SKULL
      name: "&cOnly For Me"
      slot: 15
      lore:
        - "&7Only you can use"
        - "&7this plugin"
    filler:
      material: GRAY_STAINED_GLASS_PANE
      name: " "
  smp_creator:
    title: "&8Create new SMP"
    size: 27
    privacy_toggle:
      materials:
        public: GREEN_CONCRETE
        private: RED_CONCRETE
      name: "&ePrivacy: %privacy%"
      slot: 12
      lore:
        - "&7Current: %privacy%"
        - "&7Click to toggle"
    difficulty_cycle:
      materials:
        peaceful: WOODEN_SWORD
        easy: STONE_SWORD
        normal: IRON_SWORD
        hard: DIAMOND_SWORD
      name: "&eDifficulty: %difficulty%"
      slot: 13
      lore:
        - "&7Current: %difficulty%"
        - "&7Click to cycle"
    confirm:
      material: LIME_CONCRETE
      name: "&aConfirm"
      slot: 14
      lore:
        - "&7Click to create your SMP!"
    filler:
      material: GRAY_STAINED_GLASS_PANE
      name: " "

  smp_settings:
    title: "&8SMP Settings: %name%"
    size: 27
    rename:
      material: NAME_TAG
      name: "&eRename SMP"
      slot: 11
      lore:
        - "&7Current name: &f%name%"
        - "&7Click to rename"
    privacy_toggle:
      materials:
        public: GREEN_CONCRETE
        private: RED_CONCRETE
      name: "&ePrivacy: %privacy%"
      slot: 12
      lore:
        - "&7Current: %privacy%"
        - "&7Click to toggle"
    difficulty_cycle:
      materials:
        peaceful: WOODEN_SWORD
        easy: STONE_SWORD
        normal: IRON_SWORD
        hard: DIAMOND_SWORD
      name: "&eDifficulty: %difficulty%"
      slot: 14
      lore:
        - "&7Current: %difficulty%"
        - "&7Click to cycle"
    delete:
      material: BARRIER
      name: "&cDelete SMP"
      slot: 15
      lore:
        - "&7Click to delete this SMP"
        - "&cThis action cannot be undone!"
    confirm_delete:
      material: RED_WOOL
      name: "&c&lConfirm Deletion"
      slot: 22
      lore:
        - "&7Click again to confirm"
        - "&cThis will delete the SMP permanently!"
    plugins:
      material: COMMAND_BLOCK
      name: "&ePlugin Settings"
      slot: 13
      lore:
        - "&7Manage plugins for this SMP"
        - "&7Click to open plugin settings"
    status:
      materials:
        active: LIME_DYE
        inactive: YELLOW_DYE
      name: "&eStatus: %status%"
      slot: 10
      lore:
        - "&7Current: %status%"
        - "&7Inactive SMPs can be edited by anyone"
        - "&7Inactive SMPs are deleted after 30 days"

    filler:
      material: GRAY_STAINED_GLASS_PANE
      name: " "
  smp_join:
    title: " &aPublic SMPs    &8-   &cPrivate SMPs"
    size: 54
    private_section:
      title_item:
        material: RED_BANNER
        name: "&cPrivate SMPs"
        slot: 8
        slots: [ 14, 15, 16, 17, 23, 24, 25, 26, 32, 33, 34, 35, 41, 42, 43, 44, 50, 51, 52, 53 ]
        lore:
          - "&7Your SMPs and clan members' SMPs"
    public_section:
      title_item:
        material: GREEN_BANNER
        name: "&aPublic SMPs"
        slot: 0
        slots: [ 9, 10, 11, 12, 18, 19, 20, 21, 27, 28, 29, 30, 36, 37, 38, 39, 45, 46, 47, 48]
        lore:
          - "&7All public SMPs"
    next_page:
      material: ARROW
      name: "&aNext Page"
      slots:
        public: 1
        private: 7
      lore:
        - "&7Click to go to next page"
    previous_page:
      material: ARROW
      name: "&aPrevious Page"
      slots:
        public: 2
        private: 6
      lore:
        - "&7Click to go to previous page"
    divider:
      material: GRAY_STAINED_GLASS_PANE
      name: " "
      slots: [4, 13, 22, 31, 40, 49]
    smp_item:
      material: PLAYER_HEAD
      name: "&b%name%"
      lore:
        - "&7Players: &f%current%&7/&f%max%"
        - "&7Privacy: &f%privacy%"
        - "&7Difficulty: &f%difficulty%"
        - ""
        - "&eClick to join!"
    filler:
      material: BLACK_STAINED_GLASS_PANE
      name: " "


enabled_commands:
  - smp
  - msg
  - tell
  - r
  - reply

invisible_permission: "smp.invisible" # Permission needed to be invisible in SMPs
vanish_command: "vanish" # Command (without /) to toggle vanish (e.g., EssentialsX vanish)

cross_smp_restrictions:
  enabled: true # Set to false to disable this check entirely
  # Define restricted commands and the argument index (0-based) where the target player's name is expected.
  # The check only runs if the sender is IN an SMP world.
  # It blocks the command if the player found at the specified index is online but NOT in the SAME SMP world as the sender.
  restricted_commands:
    msg: 0
    tell: 0
    w: 0
    whisper: 0 # Adding common aliases
    tpa: 0
    tpahere: 0
    tp: 0     # For commands like /tp <target_player> [...]
    give: 1   # For commands like /give <item> <target_player> [...]
    # Add other commands and their respective player argument index here
    # Example: mail: 1 # If /mail send <player> ...
  bypass_permission: "smp.bypass.crosscommand" # Players with this permission ignore the restriction

# Average player count tracking
average_players:
  check_interval: 600 # Seconds between player count checks (600 = 10 minutes)
  daily_task:
    enabled: true # Set to false to disable the daily average player task
    min_smp_age_hours: 48 # Minimum age of SMPs to check (in hours)
    retention_days: 7 # Number of days of data to keep (including current day)
    hour: 3 # Hour of the day to run the task (0-23)
    minute: 0 # Minute of the hour to run the task (0-59)

# Debug logging
debug_logging: true # Set to true to enable detailed debug logging

# Cooldown settings (in seconds)
join_cooldown: 5 # Cooldown between joining SMPs
combat_join_cooldown: 10 # Cooldown for joining SMPs after being hit

# Command-specific cooldowns (in seconds)
command_cooldowns:
  "smp create": 10 # Cooldown for /smp create command
  # Add more commands with their cooldowns here
  # Format: "command": cooldown_in_seconds

# PlaceholderAPI settings
placeholderapi:
  spawn_text: "Spawn" # Text to display when player is not in an SMP world

# Agreement book settings
agreement_book:
  title: "&6SMP Agreement"
  author: "Server"
  pages:
    - "&l&6SMP Agreement\n\n&0By creating an SMP, you agree to follow all server rules and guidelines.\n\nYou understand that your SMP may be deleted if it violates any rules or remains inactive for too long."
    - "&0You also agree that:\n\n1. You will not use your SMP to bypass server rules\n\n2. You will not use your SMP to grief or harass other players\n\n3. You will maintain a respectful environment"
    - "&0Please confirm that you agree to these terms by clicking the button below.\n\n&cIf you do not agree, please click decline."
  accept_button: "&a[ACCEPT]"
  accept_hover: "&aI agree to the terms"
  decline_button: "&c[DECLINE]"
  decline_hover: "&cI do not agree to the terms"
